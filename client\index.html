<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>Secure Password - Random Password Generator</title>
    <meta name="description" content="Create strong and secure passwords with our free random password generator tool to protect your online accounts." />
    <meta name="keywords" content="password generator, random password, secure password, strong password, password security, online tool" />

    <!-- Mobile device optimization -->
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#0086ff">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Search engine optimization -->
    <meta name="robots" content="index, follow">
    <meta name="googlebot" content="index, follow">

    <!-- Canonical link - prevent duplicate content -->
    <link rel="canonical" href="https://password-generator.me/" />

    <!-- Multilingual support - hreflang tags -->
    <link rel="alternate" hrefLang="en" href="https://password-generator.me/" />
    <link rel="alternate" hrefLang="zh" href="https://password-generator.me/zh" />
    <link rel="alternate" hrefLang="es" href="https://password-generator.me/es" />
    <link rel="alternate" hrefLang="de" href="https://password-generator.me/de" />
    <link rel="alternate" hrefLang="fr" href="https://password-generator.me/fr" />
    <link rel="alternate" hrefLang="ja" href="https://password-generator.me/ja" />
    <link rel="alternate" hrefLang="pt" href="https://password-generator.me/pt" />
    <link rel="alternate" hrefLang="ru" href="https://password-generator.me/ru" />
    <link rel="alternate" hrefLang="ar" href="https://password-generator.me/ar" />
    <link rel="alternate" hrefLang="hi" href="https://password-generator.me/hi" />
    <link rel="alternate" hrefLang="x-default" href="https://password-generator.me/" />

    <!-- Open Graph tags - for social media sharing -->
    <meta property="og:title" content="Secure Password - Random Password Generator" />
    <meta property="og:description" content="Create strong and secure passwords with our free random password generator tool to protect your online accounts." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://password-generator.me" />
    <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:locale:alternate" content="zh_CN" />
    <meta property="og:locale:alternate" content="es_ES" />
    <meta property="og:locale:alternate" content="de_DE" />
    <meta property="og:locale:alternate" content="fr_FR" />
    <meta property="og:locale:alternate" content="ja_JP" />
    <meta property="og:locale:alternate" content="pt_BR" />
    <meta property="og:locale:alternate" content="ru_RU" />
    <meta property="og:locale:alternate" content="ar_SA" />
    <meta property="og:locale:alternate" content="hi_IN" />
    <meta property="og:site_name" content="Password Generator" />

    <!-- Twitter card tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Secure Password - Random Password Generator" />
    <meta name="twitter:description" content="Create strong and secure passwords with our free random password generator tool to protect your online accounts." />
    <meta name="twitter:image" content="https://password-generator.me/og-image.jpg" />

    <!-- Favicon - comprehensive cross-browser and device support -->
    <!-- SVG icon - modern browsers -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">

    <!-- Legacy browser support -->
    <link rel="alternate icon" href="/favicon.ico">

    <!-- iOS device support -->
    <link rel="apple-touch-icon" sizes="180x180" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDA4NmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggZD0iTTEyIDIyczhLTQgOC0xMFY1bC04LTMtOCAzdjdjMCA2IDggMTAgOCAxMHoiIC8+PC9zdmc+">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Password Generator">

    <!-- Fallback inline SVG icon - ensure display in all cases -->
    <link rel="icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDA4NmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggZD0iTTEyIDIyczhLTQgOC0xMFY1bC04LTMtOCAzdjdjMCA2IDggMTAgOCAxMHoiIC8+PC9zdmc+">

    <!-- Web app manifest - for PWA support -->
    <link rel="manifest" href="/manifest.json">

    <!-- Theme color -->
    <meta name="theme-color" content="#0086ff">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C03W71YGEY"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-C03W71YGEY');
    </script>

    <script>
      // Ensure favicon displays correctly in all browsers
      document.addEventListener('DOMContentLoaded', function() {
        // Detect if browser supports SVG favicon
        var supportsSvgFavicon = document.implementation.hasFeature('http://www.w3.org/TR/SVG11/feature#Image', '1.1');

        if (!supportsSvgFavicon) {
          // If SVG favicon is not supported, use base64 encoded SVG as fallback
          var link = document.createElement('link');
          link.rel = 'icon';
          link.href = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDA4NmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggZD0iTTEyIDIyczhLTQgOC0xMFY1bC04LTMtOCAzdjdjMCA2IDggMTAgOCAxMHoiIC8+PC9zdmc+';
          document.head.appendChild(link);
        }
      });
    </script>
    <!-- Stylesheets -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
