import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FAQItem } from '@/types';
import { ChevronDown } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

const FAQ = () => {
  const { t } = useTranslation();
  
  const faqItems: FAQItem[] = [
    {
      question: t('faq.items.safe.question'),
      answer: t('faq.items.safe.answer')
    },
    {
      question: t('faq.items.why.question'),
      answer: t('faq.items.why.answer')
    },
    {
      question: t('faq.items.unique.question'),
      answer: t('faq.items.unique.answer')
    },
    {
      question: t('faq.items.worst.question'),
      answer: t('faq.items.worst.answer')
    },
    {
      question: t('faq.items.requirements.question'),
      answer: t('faq.items.requirements.answer')
    }
  ];
  
  return (
    <section className="mb-16">
      <h2 className="text-2xl font-bold mb-8 text-center">
        {t('faq.title')}
      </h2>
      <div className="space-y-4">
        <Accordion type="single" collapsible className="w-full">
          {faqItems.map((item, index) => (
            <AccordionItem 
              key={index} 
              value={`item-${index}`}
              className="border border-light-gray rounded-lg overflow-hidden bg-white mb-2"
            >
              <AccordionTrigger className="px-6 py-4 hover:bg-lighter-gray transition-colors">
                <span className="font-medium text-left">{item.question}</span>
              </AccordionTrigger>
              <AccordionContent className="px-6 py-4 bg-lighter-gray">
                <div dangerouslySetInnerHTML={{ __html: item.answer }} />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

export default FAQ;
