import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'wouter';
import { Shield } from 'lucide-react';

const Footer = () => {
  const { t } = useTranslation();

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-dark text-white py-10">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-center md:justify-between items-center mb-8 gap-4 md:gap-8">
          <div className="text-center md:text-left mb-6 md:mb-0 md:ml-8">
            <div className="flex items-center justify-center md:justify-start space-x-2 mb-4">
              <Shield className="text-primary h-6 w-6" />
              <span className="font-bold text-xl">{t('footer.name')}</span>
            </div>
            <p className="text-gray-400 max-w-xs">
              {t('footer.tagline')}
            </p>
          </div>
          <div className="flex flex-col items-center md:items-end md:mr-8">
            <h3 className="text-sm font-semibold uppercase tracking-wider mb-4 text-primary">
              {t('footer.resources.title')}
            </h3>
            <ul className="space-y-2 text-center md:text-right">
              <li>
                <Link href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.company.privacyPolicy')}
                </Link>
              </li>
              <li>
                <Link href="/terms-of-service" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.company.terms')}
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-700 pt-8 flex justify-center">
          <p className="text-gray-400 text-center">
            &copy; {currentYear} {t('footer.name')}. {t('footer.copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
