import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'wouter';
import { Shield, Globe } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { LanguageOption } from '@/types';

const Header = () => {
  const { t, i18n } = useTranslation();
  const [location, setLocation] = useLocation();

  const languages: LanguageOption[] = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' }
  ];

  // 从URL路径中提取语言代码
  useEffect(() => {
    const pathLang = location.split('/')[1];
    if (pathLang && languages.some(lang => lang.code === pathLang) && pathLang !== i18n.language) {
      i18n.changeLanguage(pathLang);
    } else if (!pathLang && i18n.language !== 'en') {
      // If no language in path, set to English
      i18n.changeLanguage('en');
    }
  }, [location, i18n]);

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);

    // 获取当前路径，保持在同一页面但更改语言
    const currentPath = location.split('/').filter(Boolean);
    let newPath = '';

    // 检查当前路径是否包含语言代码
    const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

    // 如果当前路径包含语言代码，则替换它；否则添加语言代码
    if (hasLangCode) {
      // 移除当前语言代码
      const pathWithoutLang = currentPath.slice(1).join('/');

      if (lng === 'en') {
        // 英语作为默认语言，使用根路径加上当前页面路径
        newPath = pathWithoutLang ? `/${pathWithoutLang}` : '/';
      } else {
        // 其他语言，添加语言代码
        newPath = `/${lng}${pathWithoutLang ? '/' + pathWithoutLang : ''}`;
      }
    } else {
      // 当前路径不包含语言代码
      const currentPagePath = currentPath.join('/');

      if (lng === 'en') {
        // 英语作为默认语言
        newPath = currentPagePath ? `/${currentPagePath}` : '/';
      } else {
        // 其他语言
        newPath = `/${lng}${currentPagePath ? '/' + currentPagePath : ''}`;
      }
    }

    setLocation(newPath);
  };

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];
  const currentPath = location.split('/').filter(Boolean);
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  return (
    <header className="bg-dark text-white">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
          <Shield className="text-primary h-6 w-6" />
          <span className="font-bold text-xl">{t('footer.name')}</span>
        </Link>
        <div className="flex items-center space-x-4">
          <Link
            href={hasLangCode && currentPath[0] !== 'en' ? `/${i18n.language}/blog` : '/blog'}
            className="mr-2 px-3 py-1 rounded bg-primary text-white hover:bg-primary-dark transition-colors text-sm font-medium"
          >
            {t('blog.button')}
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center text-sm font-medium space-x-1 bg-dark-light border-none text-white">
                <Globe className="h-4 w-4 mr-1" />
                <span>{currentLanguage.nativeName}</span>
                <span className="ml-1 i-fas-chevron-down text-xs"></span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {languages.map((language) => (
                <DropdownMenuItem
                  key={language.code}
                  onClick={() => changeLanguage(language.code)}
                  className="cursor-pointer"
                >
                  {language.nativeName} {language.name !== language.nativeName && `(${language.name})`}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default Header;
