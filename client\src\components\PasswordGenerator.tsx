import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Co<PERSON>, Minus, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { PasswordOptions, PasswordStrength } from '@/types';
import { generatePassword, evaluatePasswordStrength } from '@/lib/password-generator';
import { useToast } from '@/hooks/use-toast';

const PasswordGenerator = () => {
  const { t } = useTranslation();
  const { toast } = useToast();

  const [passwordOptions, setPasswordOptions] = useState<PasswordOptions>({
    length: 15,
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true
  });

  const [password, setPassword] = useState('');
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    label: '',
    color: '',
    percentage: 0
  });
  const [copied, setCopied] = useState(false);

  // Generate initial password
  useEffect(() => {
    generateNewPassword();
  }, []);

  // Generate a new password when options change
  useEffect(() => {
    if (
      passwordOptions.uppercase ||
      passwordOptions.lowercase ||
      passwordOptions.numbers ||
      passwordOptions.symbols
    ) {
      generateNewPassword();
    }
  }, [passwordOptions]);

  // Evaluate password strength whenever password changes
  useEffect(() => {
    if (password) {
      const strength = evaluatePasswordStrength(password);
      setPasswordStrength(strength);
    }
  }, [password]);

  const generateNewPassword = useCallback(() => {
    if (
      !passwordOptions.uppercase &&
      !passwordOptions.lowercase &&
      !passwordOptions.numbers &&
      !passwordOptions.symbols
    ) {
      // Ensure at least one option is selected
      setPasswordOptions(prev => ({ ...prev, lowercase: true }));
      return;
    }

    const newPassword = generatePassword(passwordOptions);
    setPassword(newPassword);
  }, [passwordOptions]);

  const handleLengthChange = (value: number[]) => {
    setPasswordOptions(prev => ({ ...prev, length: value[0] }));
  };

  const decreaseLength = () => {
    if (passwordOptions.length > 6) {
      setPasswordOptions(prev => ({ ...prev, length: prev.length - 1 }));
    }
  };

  const increaseLength = () => {
    if (passwordOptions.length < 30) {
      setPasswordOptions(prev => ({ ...prev, length: prev.length + 1 }));
    }
  };

  const handleCopyPassword = async () => {
    try {
      await navigator.clipboard.writeText(password);
      setCopied(true);
      toast({
        description: t('passwordGenerator.copiedToClipboard'),
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast({
        variant: "destructive",
        description: t('passwordGenerator.copyFailed'),
      });
    }
  };

  const handleCheckboxChange = (name: keyof Omit<PasswordOptions, 'length'>) => {
    setPasswordOptions(prev => {
      const updated = { ...prev, [name]: !prev[name] };

      // Ensure at least one option is selected
      if (!updated.uppercase && !updated.lowercase && !updated.numbers && !updated.symbols) {
        return prev;
      }

      return updated;
    });
  };

  return (
    <section className="bg-white rounded-xl shadow-md overflow-hidden mb-16">
      <div className="md:flex">
        <div className="md:w-2/5 p-6 md:p-8 bg-lighter-gray flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 500 500"
            className="w-full max-w-xs mx-auto"
            aria-hidden="true"
          >
            <g fill="none" stroke="#000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2">
              <path d="M250 400c83 0 150-67 150-150S333 100 250 100s-150 67-150 150 67 150 150 150z" fill="#f0f4f9"/>
              <path d="M225 280h50v40h-50z" fill="#0086ff"/>
              <path d="M170 320c0-44 36-80 80-80s80 36 80 80v30H170v-30z" fill="#fff"/>
              <path d="M250 240a80 80 0 00-80 80v30h160v-30a80 80 0 00-80-80z"/>
              <path d="M250 290a10 10 0 0110 10v30a10 10 0 01-20 0v-30a10 10 0 0110-10z" fill="#0086ff"/>
              <path d="M200 190c11-11 29-11 40 0M270 190c11-11 29-11 40 0"/>
              <circle cx="250" cy="170" r="70" fill="#fff"/>
              <path d="M250 170a70 70 0 100-140 70 70 0 000 140z" fill="#fff"/>
            </g>
          </svg>
        </div>
        <div className="md:w-3/5 p-6 md:p-8">
          <div className="mb-6">
            <div className="relative">
              <input
                type="text"
                id="generatedPassword"
                value={password}
                readOnly
                className="w-full border border-light-gray rounded-lg py-3 px-4 pr-14 bg-lighter-gray font-mono text-lg"
                aria-label={t('passwordGenerator.generatedPassword')}
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <Button
                  onClick={handleCopyPassword}
                  variant="ghost"
                  size="icon"
                  aria-label={t('passwordGenerator.copyPassword')}
                  className="text-primary hover:text-blue-700 p-2 rounded-md transition-colors"
                >
                  {copied ? <span aria-hidden="true">✓</span> : <Copy size={18} />}
                </Button>
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <div className="flex-1 h-2 bg-light-gray rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full`}
                  style={{
                    width: `${passwordStrength.percentage}%`,
                    backgroundColor: passwordStrength.color
                  }}
                ></div>
              </div>
              <span className={`ml-3 text-sm font-medium`} style={{ color: passwordStrength.color }}>
                {t(`passwordStrength.${passwordStrength.label}`)}
              </span>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <Label htmlFor="passwordLength" className="text-sm font-medium">
                {t('passwordGenerator.passwordLength')}:
              </Label>
              <span id="lengthValue" className="text-sm font-medium bg-lighter-gray px-2 py-1 rounded">
                {passwordOptions.length}
              </span>
            </div>
            <div className="flex items-center">
              <Button
                onClick={decreaseLength}
                variant="ghost"
                size="icon"
                aria-label={t('passwordGenerator.decreaseLength')}
                className="text-dark-light hover:text-dark p-1"
              >
                <Minus size={16} />
              </Button>
              <div className="flex-1 mx-3">
                <Slider
                  id="passwordLength"
                  min={6}
                  max={30}
                  step={1}
                  value={[passwordOptions.length]}
                  onValueChange={handleLengthChange}
                  aria-label={t('passwordGenerator.passwordLength')}
                  className="w-full accent-primary"
                />
              </div>
              <Button
                onClick={increaseLength}
                variant="ghost"
                size="icon"
                aria-label={t('passwordGenerator.increaseLength')}
                className="text-dark-light hover:text-dark p-1"
              >
                <Plus size={16} />
              </Button>
            </div>
          </div>

          <div className="mb-2">
            <Label className="text-sm font-medium">
              {t('passwordGenerator.charactersUsed')}:
            </Label>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="flex items-center">
              <Checkbox
                id="uppercase"
                checked={passwordOptions.uppercase}
                onCheckedChange={() => handleCheckboxChange('uppercase')}
                className="h-5 w-5 rounded border-light-gray text-primary focus:ring-primary"
              />
              <Label htmlFor="uppercase" className="ml-2 text-sm">ABC</Label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="lowercase"
                checked={passwordOptions.lowercase}
                onCheckedChange={() => handleCheckboxChange('lowercase')}
                className="h-5 w-5 rounded border-light-gray text-primary focus:ring-primary"
              />
              <Label htmlFor="lowercase" className="ml-2 text-sm">abc</Label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="numbers"
                checked={passwordOptions.numbers}
                onCheckedChange={() => handleCheckboxChange('numbers')}
                className="h-5 w-5 rounded border-light-gray text-primary focus:ring-primary"
              />
              <Label htmlFor="numbers" className="ml-2 text-sm">123</Label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="symbols"
                checked={passwordOptions.symbols}
                onCheckedChange={() => handleCheckboxChange('symbols')}
                className="h-5 w-5 rounded border-light-gray text-primary focus:ring-primary"
              />
              <Label htmlFor="symbols" className="ml-2 text-sm">#$&</Label>
            </div>
          </div>

          <div className="mt-6">
            <Button
              onClick={generateNewPassword}
              className="w-full bg-primary hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-colors"
            >
              {t('passwordGenerator.generateNew')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PasswordGenerator;
