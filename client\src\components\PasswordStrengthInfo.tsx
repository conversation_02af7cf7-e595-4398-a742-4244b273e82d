import { useTranslation } from 'react-i18next';
import { Ruler, Lock, Fingerprint } from 'lucide-react';

const PasswordStrengthInfo = () => {
  const { t } = useTranslation();
  
  return (
    <section className="mb-16">
      <h2 className="text-2xl font-bold mb-8 text-center">
        {t('passwordStrengthInfo.title')}
      </h2>
      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm text-center">
          <div className="bg-lighter-gray inline-flex items-center justify-center w-16 h-16 rounded-full mb-4">
            <Ruler className="text-primary text-2xl" />
          </div>
          <h3 className="text-lg font-semibold mb-2">
            {t('passwordStrengthInfo.length.title')}
          </h3>
          <p className="text-dark-light">
            {t('passwordStrengthInfo.length.description')}
          </p>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm text-center">
          <div className="bg-lighter-gray inline-flex items-center justify-center w-16 h-16 rounded-full mb-4">
            <Lock className="text-primary text-2xl" />
          </div>
          <h3 className="text-lg font-semibold mb-2">
            {t('passwordStrengthInfo.complex.title')}
          </h3>
          <p className="text-dark-light">
            {t('passwordStrengthInfo.complex.description')}
          </p>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm text-center">
          <div className="bg-lighter-gray inline-flex items-center justify-center w-16 h-16 rounded-full mb-4">
            <Fingerprint className="text-primary text-2xl" />
          </div>
          <h3 className="text-lg font-semibold mb-2">
            {t('passwordStrengthInfo.unique.title')}
          </h3>
          <p className="text-dark-light">
            {t('passwordStrengthInfo.unique.description')}
          </p>
        </div>
      </div>
    </section>
  );
};

export default PasswordStrengthInfo;
