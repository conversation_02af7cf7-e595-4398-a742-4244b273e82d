import { useTranslation } from 'react-i18next';
import { CheckCircle } from 'lucide-react';

const SecurityTips = () => {
  const { t } = useTranslation();
  
  const securityTips = [
    t('securityTips.different'),
    t('securityTips.manager'),
    t('securityTips.twoFactor'),
    t('securityTips.breach'),
    t('securityTips.personal'),
    t('securityTips.passphrase')
  ];
  
  return (
    <section className="bg-white rounded-xl shadow-md overflow-hidden mb-16">
      <div className="md:flex">
        <div className="md:w-3/5 p-6 md:p-8">
          <h2 className="text-2xl font-bold mb-4">
            {t('securityTips.title')}
          </h2>
          <ul className="space-y-3">
            {securityTips.map((tip, index) => (
              <li key={index} className="flex items-start">
                <CheckCircle className="text-secondary mt-1 mr-3 h-5 w-5" />
                <span>{tip}</span>
              </li>
            ))}
          </ul>
        </div>
        <div className="md:w-2/5 p-6 md:p-8 bg-lighter-gray flex items-center justify-center">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 500 500" 
            className="w-full max-w-xs mx-auto"
            aria-hidden="true"
          >
            <g fill="none" stroke="#000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2">
              <path d="M250 100c-83 0-150 67-150 150s67 150 150 150 150-67 150-150-67-150-150-150z" fill="#f0f4f9"/>
              <path d="M175 250l35 35 65-65" stroke="#0086ff" strokeWidth="8"/>
              <path d="M175 200h150v150H175z" fill="#fff"/>
              <path d="M250 180a70 70 0 00-70 70v50h140v-50a70 70 0 00-70-70z" fill="#fff"/>
              <path d="M250 230a10 10 0 00-10 10v40a10 10 0 0020 0v-40a10 10 0 00-10-10z" fill="#0086ff"/>
            </g>
          </svg>
        </div>
      </div>
    </section>
  );
};

export default SecurityTips;
