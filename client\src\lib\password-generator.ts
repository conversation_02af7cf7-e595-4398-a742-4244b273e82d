import { PasswordOptions, PasswordStrength } from '@/types';

// Character sets for password generation
const UPPERCASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const LOWERCASE_CHARS = 'abcdefghijklmnopqrstuvwxyz';
const NUMBER_CHARS = '0123456789';
const SYMBOL_CHARS = '!@#$%^&*()_+~`|}{[]:;?><,./-=';

/**
 * Generates a cryptographically secure random password based on options
 */
export function generatePassword(options: PasswordOptions): string {
  const { length, uppercase, lowercase, numbers, symbols } = options;

  // Construct character set based on selected options
  let chars = '';
  if (uppercase) chars += UPPERCASE_CHARS;
  if (lowercase) chars += LOWERCASE_CHARS;
  if (numbers) chars += NUMBER_CHARS;
  if (symbols) chars += SYMBOL_CHARS;

  // Fallback to lowercase if nothing is selected (shouldn't happen due to UI constraints)
  if (!chars) chars = LOWERCASE_CHARS;

  // Generate password
  let password = '';
  const array = new Uint32Array(length);
  crypto.getRandomValues(array);

  for (let i = 0; i < length; i++) {
    password += chars[array[i] % chars.length];
  }

  // Ensure at least one character from each selected type
  const requiredChars = [];
  if (uppercase) requiredChars.push(getRandomChar(UPPERCASE_CHARS));
  if (lowercase) requiredChars.push(getRandomChar(LOWERCASE_CHARS));
  if (numbers) requiredChars.push(getRandomChar(NUMBER_CHARS));
  if (symbols) requiredChars.push(getRandomChar(SYMBOL_CHARS));

  // Replace first n characters with required characters
  for (let i = 0; i < requiredChars.length; i++) {
    password = password.substring(0, i) + requiredChars[i] + password.substring(i + 1);
  }

  return password;
}

/**
 * Gets a random character from the provided character set
 */
function getRandomChar(chars: string): string {
  const array = new Uint32Array(1);
  crypto.getRandomValues(array);
  return chars[array[0] % chars.length];
}

/**
 * Evaluates password strength based on multiple factors
 */
export function evaluatePasswordStrength(password: string): PasswordStrength {
  if (!password) {
    return {
      score: 0,
      label: 'Weak',
      color: '#ff6b4a', // red
      percentage: 0
    };
  }

  let score = 0;

  // Length check
  if (password.length < 8) {
    score += 0;
  } else if (password.length < 10) {
    score += 1;
  } else if (password.length < 12) {
    score += 2;
  } else if (password.length < 14) {
    score += 3;
  } else {
    score += 4;
  }

  // Character variety checks
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSymbol = /[^A-Za-z0-9]/.test(password);

  const variety = [hasLower, hasUpper, hasNumber, hasSymbol].filter(Boolean).length;

  // Add points for character variety
  score += variety;

  // Repetition and pattern checks
  const repeatingChars = /(.)(\1{2,})/; // Three or more of the same char
  const sequences = /(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|012|123|234|345|456|567|678|789)/i;

  if (repeatingChars.test(password)) {
    score -= 1;
  }

  if (sequences.test(password)) {
    score -= 1;
  }

  // Normalize score to 0-4 range
  score = Math.max(0, Math.min(score, 4));

  // Map score to user-friendly evaluation
  const strengthMap: Record<number, { label: string; color: string; percentage: number }> = {
    0: { label: 'veryweak', color: '#ff6b4a', percentage: 20 }, // red
    1: { label: 'weak', color: '#ffa500', percentage: 40 }, // orange
    2: { label: 'medium', color: '#ffcc00', percentage: 60 }, // yellow
    3: { label: 'strong', color: '#9bc158', percentage: 80 }, // light green
    4: { label: 'verystrong', color: '#00c16e', percentage: 100 } // green
  };

  return {
    score,
    ...strengthMap[score]
  };
}
