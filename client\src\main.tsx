import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import "./styles/global.css";
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';

// Ensure default language is English if not set
if (!localStorage.getItem('i18nextLng')) {
  localStorage.setItem('i18nextLng', 'en');
  i18n.changeLanguage('en');
}

createRoot(document.getElementById("root")!).render(
  <I18nextProvider i18n={i18n}>
    <App />
  </I18nextProvider>
);
