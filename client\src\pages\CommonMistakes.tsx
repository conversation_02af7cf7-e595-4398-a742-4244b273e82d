import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { useLocation } from 'wouter';

const CommonMistakes = () => {
  const { t, i18n } = useTranslation();
  const [location] = useLocation();

  useEffect(() => {
    const lang = location.split('/')[1];
    const languages = ['en', 'zh', 'es', 'de', 'fr', 'ja', 'pt', 'ru', 'ar', 'hi'];
    if (lang && languages.includes(lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [location, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.commonMistakes.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.commonMistakes.content')} />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-3xl flex-grow">
          <h1 className="text-3xl font-bold mb-6">{t('blog.commonMistakes.title')}</h1>
          <p className="mb-8 text-lg text-dark-light">{t('blog.commonMistakes.content')}</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>{t('blog.commonMistakes.detail1')}</li>
            <li>{t('blog.commonMistakes.detail2')}</li>
            <li>{t('blog.commonMistakes.detail3')}</li>
            <li>{t('blog.commonMistakes.detail4')}</li>
          </ul>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default CommonMistakes;