import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { Shield, AlertTriangle, Search, Lock } from 'lucide-react';

const DataBreachProtection = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.dataBreachProtection.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.dataBreachProtection.content').slice(0, 160)} />
        <meta name="keywords" content="data breach, protection, cybersecurity, privacy, 数据泄露, 防护, 网络安全, 隐私" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/data-breach-protection`} />
        <meta property="og:title" content={`${t('blog.dataBreachProtection.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.dataBreachProtection.content').slice(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/data-breach-protection`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <article className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('blog.dataBreachProtection.title')}</h1>
            
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-red-400 mr-2 mt-0.5" />
                <p className="text-red-700 text-sm">
                  {i18n.language === 'zh' ? '数据泄露可能导致严重的隐私和财务损失。了解如何保护自己至关重要。' : 'Data breaches can lead to serious privacy and financial losses. Understanding how to protect yourself is crucial.'}
                </p>
              </div>
            </div>

            <div className="text-gray-700 leading-relaxed mb-8">
              {t('blog.dataBreachProtection.content').split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Search className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {i18n.language === 'zh' ? '检查数据泄露' : 'Check for Breaches'}
                  </h3>
                </div>
                <p className="text-blue-800 text-sm">{t('blog.dataBreachProtection.detail1')}</p>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-900">
                    {i18n.language === 'zh' ? '立即响应' : 'Immediate Response'}
                  </h3>
                </div>
                <p className="text-green-800 text-sm">{t('blog.dataBreachProtection.detail2')}</p>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Lock className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-purple-900">
                    {i18n.language === 'zh' ? '限制信息分享' : 'Limit Information Sharing'}
                  </h3>
                </div>
                <p className="text-purple-800 text-sm">{t('blog.dataBreachProtection.detail3')}</p>
              </div>

              <div className="bg-orange-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-orange-600 mr-2" />
                  <h3 className="text-lg font-semibold text-orange-900">
                    {i18n.language === 'zh' ? '保持更新' : 'Stay Updated'}
                  </h3>
                </div>
                <p className="text-orange-800 text-sm">{t('blog.dataBreachProtection.detail4')}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {i18n.language === 'zh' ? '推荐工具' : 'Recommended Tools'}
              </h3>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Have I Been Pwned - {i18n.language === 'zh' ? '检查数据泄露' : 'Check for data breaches'}</li>
                <li>Firefox Monitor - {i18n.language === 'zh' ? '监控账户安全' : 'Monitor account security'}</li>
                <li>Google Password Checkup - {i18n.language === 'zh' ? '密码安全检查' : 'Password security check'}</li>
                <li>Identity Guard - {i18n.language === 'zh' ? '身份监控服务' : 'Identity monitoring service'}</li>
              </ul>
            </div>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default DataBreachProtection;
