import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { Building, Shield, Users, AlertTriangle } from 'lucide-react';

const EnterprisePasswordSecurity = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.enterprisePasswordSecurity.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.enterprisePasswordSecurity.content').slice(0, 160)} />
        <meta name="keywords" content="enterprise security, password policy, SSO, PAM, 企业安全, 密码策略, 单点登录" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/enterprise-password-security`} />
        <meta property="og:title" content={`${t('blog.enterprisePasswordSecurity.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.enterprisePasswordSecurity.content').slice(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/enterprise-password-security`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <article className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('blog.enterprisePasswordSecurity.title')}</h1>
            
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-red-400 mr-2 mt-0.5" />
                <p className="text-red-700 text-sm">
                  {i18n.language === 'zh' ? '企业数据泄露的平均成本已超过400万美元。建立强大的密码安全策略至关重要。' : 'The average cost of enterprise data breaches now exceeds $4 million. Establishing robust password security policies is crucial.'}
                </p>
              </div>
            </div>

            <div className="text-gray-700 leading-relaxed mb-8">
              {t('blog.enterprisePasswordSecurity.content').split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Building className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {i18n.language === 'zh' ? '密码策略' : 'Password Policy'}
                  </h3>
                </div>
                <p className="text-blue-800 text-sm">{t('blog.enterprisePasswordSecurity.detail1')}</p>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-900">
                    {i18n.language === 'zh' ? '技术解决方案' : 'Technical Solutions'}
                  </h3>
                </div>
                <p className="text-green-800 text-sm">{t('blog.enterprisePasswordSecurity.detail2')}</p>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Users className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-purple-900">
                    {i18n.language === 'zh' ? '员工培训' : 'Employee Training'}
                  </h3>
                </div>
                <p className="text-purple-800 text-sm">{t('blog.enterprisePasswordSecurity.detail3')}</p>
              </div>

              <div className="bg-orange-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <AlertTriangle className="h-6 w-6 text-orange-600 mr-2" />
                  <h3 className="text-lg font-semibold text-orange-900">
                    {i18n.language === 'zh' ? '事件响应' : 'Incident Response'}
                  </h3>
                </div>
                <p className="text-orange-800 text-sm">{t('blog.enterprisePasswordSecurity.detail4')}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {i18n.language === 'zh' ? '企业密码策略要素' : 'Enterprise Password Policy Elements'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '密码复杂性要求' : 'Password Complexity Requirements'}
                  </h4>
                  <ul className="text-gray-600 text-sm space-y-1">
                    <li>• {i18n.language === 'zh' ? '最少12位字符' : 'Minimum 12 characters'}</li>
                    <li>• {i18n.language === 'zh' ? '包含大小写字母、数字、符号' : 'Include uppercase, lowercase, numbers, symbols'}</li>
                    <li>• {i18n.language === 'zh' ? '禁止使用常见密码' : 'Prohibit common passwords'}</li>
                  </ul>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '访问控制' : 'Access Control'}
                  </h4>
                  <ul className="text-gray-600 text-sm space-y-1">
                    <li>• {i18n.language === 'zh' ? '基于角色的权限管理' : 'Role-based permission management'}</li>
                    <li>• {i18n.language === 'zh' ? '最小权限原则' : 'Principle of least privilege'}</li>
                    <li>• {i18n.language === 'zh' ? '定期权限审查' : 'Regular permission reviews'}</li>
                  </ul>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '监控和审计' : 'Monitoring and Auditing'}
                  </h4>
                  <ul className="text-gray-600 text-sm space-y-1">
                    <li>• {i18n.language === 'zh' ? '登录活动监控' : 'Login activity monitoring'}</li>
                    <li>• {i18n.language === 'zh' ? '异常行为检测' : 'Anomaly detection'}</li>
                    <li>• {i18n.language === 'zh' ? '安全事件日志' : 'Security event logging'}</li>
                  </ul>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '合规性要求' : 'Compliance Requirements'}
                  </h4>
                  <ul className="text-gray-600 text-sm space-y-1">
                    <li>• GDPR {i18n.language === 'zh' ? '数据保护' : 'data protection'}</li>
                    <li>• SOX {i18n.language === 'zh' ? '财务合规' : 'financial compliance'}</li>
                    <li>• ISO 27001 {i18n.language === 'zh' ? '信息安全' : 'information security'}</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-blue-900">
                {i18n.language === 'zh' ? '推荐的企业解决方案' : 'Recommended Enterprise Solutions'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">SSO {i18n.language === 'zh' ? '单点登录' : 'Single Sign-On'}</h4>
                  <p className="text-gray-600 text-sm mb-2">Okta, Azure AD, Auth0</p>
                  <p className="text-gray-600 text-xs">
                    {i18n.language === 'zh' ? '简化用户体验，集中身份管理' : 'Simplify user experience, centralized identity management'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">PAM {i18n.language === 'zh' ? '特权访问管理' : 'Privileged Access Management'}</h4>
                  <p className="text-gray-600 text-sm mb-2">CyberArk, BeyondTrust, Thycotic</p>
                  <p className="text-gray-600 text-xs">
                    {i18n.language === 'zh' ? '保护高权限账户，监控特权操作' : 'Protect high-privilege accounts, monitor privileged operations'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">{i18n.language === 'zh' ? '企业密码管理器' : 'Enterprise Password Managers'}</h4>
                  <p className="text-gray-600 text-sm mb-2">1Password Business, Bitwarden Business</p>
                  <p className="text-gray-600 text-xs">
                    {i18n.language === 'zh' ? '团队密码共享，集中管理策略' : 'Team password sharing, centralized policy management'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">{i18n.language === 'zh' ? '安全培训平台' : 'Security Training Platforms'}</h4>
                  <p className="text-gray-600 text-sm mb-2">KnowBe4, Proofpoint, Cofense</p>
                  <p className="text-gray-600 text-xs">
                    {i18n.language === 'zh' ? '员工安全意识培训，钓鱼模拟' : 'Employee security awareness training, phishing simulation'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-yellow-900">
                {i18n.language === 'zh' ? '实施建议' : 'Implementation Recommendations'}
              </h3>
              <ol className="list-decimal list-inside text-yellow-800 space-y-2">
                <li>{i18n.language === 'zh' ? '评估当前安全状况，识别风险点' : 'Assess current security posture and identify risk points'}</li>
                <li>{i18n.language === 'zh' ? '制定详细的密码策略文档' : 'Develop detailed password policy documentation'}</li>
                <li>{i18n.language === 'zh' ? '选择合适的技术解决方案' : 'Choose appropriate technical solutions'}</li>
                <li>{i18n.language === 'zh' ? '分阶段实施，从关键系统开始' : 'Implement in phases, starting with critical systems'}</li>
                <li>{i18n.language === 'zh' ? '开展全员安全培训' : 'Conduct company-wide security training'}</li>
                <li>{i18n.language === 'zh' ? '建立监控和响应机制' : 'Establish monitoring and response mechanisms'}</li>
                <li>{i18n.language === 'zh' ? '定期审查和更新策略' : 'Regularly review and update policies'}</li>
              </ol>
            </div>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default EnterprisePasswordSecurity;
