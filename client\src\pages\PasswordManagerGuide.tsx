import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { Key, Shield, Smartphone, Star } from 'lucide-react';

const PasswordManagerGuide = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.passwordManagerGuide.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.passwordManagerGuide.content').slice(0, 160)} />
        <meta name="keywords" content="password manager, security, 1Password, Bitwarden, LastPass, 密码管理器, 安全" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/password-manager-guide`} />
        <meta property="og:title" content={`${t('blog.passwordManagerGuide.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.passwordManagerGuide.content').slice(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/password-manager-guide`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <article className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('blog.passwordManagerGuide.title')}</h1>
            
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-8">
              <div className="flex">
                <Key className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
                <p className="text-blue-700 text-sm">
                  {i18n.language === 'zh' ? '使用密码管理器是提升个人网络安全最有效的方法之一。' : 'Using a password manager is one of the most effective ways to improve personal cybersecurity.'}
                </p>
              </div>
            </div>

            <div className="text-gray-700 leading-relaxed mb-8">
              {t('blog.passwordManagerGuide.content').split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-900">
                    {i18n.language === 'zh' ? '安全加密' : 'Security Encryption'}
                  </h3>
                </div>
                <p className="text-green-800 text-sm">{t('blog.passwordManagerGuide.detail1')}</p>
              </div>

              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Smartphone className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {i18n.language === 'zh' ? '跨平台支持' : 'Cross-Platform Support'}
                  </h3>
                </div>
                <p className="text-blue-800 text-sm">{t('blog.passwordManagerGuide.detail2')}</p>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Key className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-purple-900">
                    {i18n.language === 'zh' ? '逐步迁移' : 'Gradual Migration'}
                  </h3>
                </div>
                <p className="text-purple-800 text-sm">{t('blog.passwordManagerGuide.detail3')}</p>
              </div>

              <div className="bg-orange-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-orange-600 mr-2" />
                  <h3 className="text-lg font-semibold text-orange-900">
                    {i18n.language === 'zh' ? '双重认证' : 'Two-Factor Authentication'}
                  </h3>
                </div>
                <p className="text-orange-800 text-sm">{t('blog.passwordManagerGuide.detail4')}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {i18n.language === 'zh' ? '主流密码管理器对比' : 'Popular Password Managers Comparison'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded border">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-800">1Password</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">
                    {i18n.language === 'zh' ? '功能丰富，界面优美，安全性极高' : 'Feature-rich, beautiful interface, extremely secure'}
                  </p>
                  <p className="text-green-600 text-xs">
                    {i18n.language === 'zh' ? '价格：$2.99/月起' : 'Price: From $2.99/month'}
                  </p>
                </div>

                <div className="bg-white p-4 rounded border">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-800">Bitwarden</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4" />
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">
                    {i18n.language === 'zh' ? '开源免费，功能完整，性价比高' : 'Open source, free, complete features, great value'}
                  </p>
                  <p className="text-green-600 text-xs">
                    {i18n.language === 'zh' ? '价格：免费版可用' : 'Price: Free version available'}
                  </p>
                </div>

                <div className="bg-white p-4 rounded border">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-800">LastPass</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4" />
                      <Star className="h-4 w-4" />
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">
                    {i18n.language === 'zh' ? '老牌产品，用户众多，但近期有安全事件' : 'Established product, large user base, but recent security incidents'}
                  </p>
                  <p className="text-green-600 text-xs">
                    {i18n.language === 'zh' ? '价格：$3/月起' : 'Price: From $3/month'}
                  </p>
                </div>

                <div className="bg-white p-4 rounded border">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-800">Dashlane</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4" />
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">
                    {i18n.language === 'zh' ? '界面友好，功能强大，包含VPN服务' : 'User-friendly interface, powerful features, includes VPN service'}
                  </p>
                  <p className="text-green-600 text-xs">
                    {i18n.language === 'zh' ? '价格：$4.99/月起' : 'Price: From $4.99/month'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-yellow-900">
                {i18n.language === 'zh' ? '迁移步骤指南' : 'Migration Step Guide'}
              </h3>
              <ol className="list-decimal list-inside text-yellow-800 space-y-2">
                <li>{i18n.language === 'zh' ? '选择合适的密码管理器并创建账户' : 'Choose a suitable password manager and create an account'}</li>
                <li>{i18n.language === 'zh' ? '安装浏览器扩展和移动应用' : 'Install browser extensions and mobile apps'}</li>
                <li>{i18n.language === 'zh' ? '从最重要的账户开始，逐个更新密码' : 'Start with the most important accounts and update passwords one by one'}</li>
                <li>{i18n.language === 'zh' ? '启用双重认证保护密码管理器账户' : 'Enable two-factor authentication for your password manager account'}</li>
                <li>{i18n.language === 'zh' ? '导入现有密码（如果有的话）' : 'Import existing passwords (if any)'}</li>
                <li>{i18n.language === 'zh' ? '定期检查和更新密码安全性' : 'Regularly check and update password security'}</li>
              </ol>
            </div>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default PasswordManagerGuide;
