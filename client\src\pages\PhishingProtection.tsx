import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { Mail, AlertTriangle, Eye, Shield } from 'lucide-react';

const PhishingProtection = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.phishingProtection.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.phishingProtection.content').slice(0, 160)} />
        <meta name="keywords" content="phishing, protection, email security, cybersecurity, 网络钓鱼, 防护, 邮件安全, 网络安全" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/phishing-protection`} />
        <meta property="og:title" content={`${t('blog.phishingProtection.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.phishingProtection.content').slice(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/phishing-protection`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <article className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('blog.phishingProtection.title')}</h1>
            
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                <p className="text-yellow-700 text-sm">
                  {i18n.language === 'zh' ? '网络钓鱼攻击每年影响数百万用户。学会识别和防范这些攻击至关重要。' : 'Phishing attacks affect millions of users annually. Learning to identify and prevent these attacks is crucial.'}
                </p>
              </div>
            </div>

            <div className="text-gray-700 leading-relaxed mb-8">
              {t('blog.phishingProtection.content').split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-red-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Eye className="h-6 w-6 text-red-600 mr-2" />
                  <h3 className="text-lg font-semibold text-red-900">
                    {i18n.language === 'zh' ? '识别可疑邮件' : 'Identify Suspicious Emails'}
                  </h3>
                </div>
                <p className="text-red-800 text-sm">{t('blog.phishingProtection.detail1')}</p>
              </div>

              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {i18n.language === 'zh' ? '安全访问' : 'Safe Access'}
                  </h3>
                </div>
                <p className="text-blue-800 text-sm">{t('blog.phishingProtection.detail2')}</p>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-900">
                    {i18n.language === 'zh' ? '使用防护工具' : 'Use Protection Tools'}
                  </h3>
                </div>
                <p className="text-green-800 text-sm">{t('blog.phishingProtection.detail3')}</p>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Mail className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-purple-900">
                    {i18n.language === 'zh' ? '验证来源' : 'Verify Sources'}
                  </h3>
                </div>
                <p className="text-purple-800 text-sm">{t('blog.phishingProtection.detail4')}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {i18n.language === 'zh' ? '常见钓鱼攻击类型' : 'Common Phishing Attack Types'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '邮件钓鱼' : 'Email Phishing'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '通过虚假邮件诱导用户点击恶意链接或下载附件' : 'Using fake emails to trick users into clicking malicious links or downloading attachments'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '短信钓鱼' : 'SMS Phishing'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '通过短信发送虚假链接，诱导用户泄露敏感信息' : 'Sending fake links via SMS to trick users into revealing sensitive information'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '网站钓鱼' : 'Website Phishing'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '创建虚假网站模仿合法服务，窃取登录凭据' : 'Creating fake websites that mimic legitimate services to steal login credentials'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '社交媒体钓鱼' : 'Social Media Phishing'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '通过社交媒体平台传播恶意链接和虚假信息' : 'Spreading malicious links and false information through social media platforms'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-blue-900">
                {i18n.language === 'zh' ? '防护工具推荐' : 'Recommended Protection Tools'}
              </h3>
              <ul className="list-disc list-inside text-blue-800 space-y-2">
                <li>Gmail/Outlook - {i18n.language === 'zh' ? '内置反钓鱼过滤器' : 'Built-in anti-phishing filters'}</li>
                <li>uBlock Origin - {i18n.language === 'zh' ? '浏览器广告和恶意网站拦截器' : 'Browser ad and malicious site blocker'}</li>
                <li>PhishTank - {i18n.language === 'zh' ? '钓鱼网站数据库查询' : 'Phishing website database lookup'}</li>
                <li>Microsoft Defender SmartScreen - {i18n.language === 'zh' ? '网页安全检查' : 'Web page security check'}</li>
              </ul>
            </div>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default PhishingProtection;
