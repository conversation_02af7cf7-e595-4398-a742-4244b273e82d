import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { Brain, Eye, Shield, Users } from 'lucide-react';

const SocialEngineeringProtection = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.socialEngineeringProtection.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.socialEngineeringProtection.content').slice(0, 160)} />
        <meta name="keywords" content="social engineering, cybersecurity, manipulation, protection, 社交工程, 网络安全, 心理操纵, 防护" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/social-engineering-protection`} />
        <meta property="og:title" content={`${t('blog.socialEngineeringProtection.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.socialEngineeringProtection.content').slice(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/social-engineering-protection`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <article className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('blog.socialEngineeringProtection.title')}</h1>
            
            <div className="bg-orange-50 border-l-4 border-orange-400 p-4 mb-8">
              <div className="flex">
                <Brain className="h-5 w-5 text-orange-400 mr-2 mt-0.5" />
                <p className="text-orange-700 text-sm">
                  {i18n.language === 'zh' ? '社交工程攻击利用人性弱点，比技术攻击更难防范。提高警觉性是最好的防护。' : 'Social engineering attacks exploit human weaknesses and are harder to defend against than technical attacks. Raising awareness is the best protection.'}
                </p>
              </div>
            </div>

            <div className="text-gray-700 leading-relaxed mb-8">
              {t('blog.socialEngineeringProtection.content').split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-red-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Brain className="h-6 w-6 text-red-600 mr-2" />
                  <h3 className="text-lg font-semibold text-red-900">
                    {i18n.language === 'zh' ? '识别攻击技巧' : 'Identify Attack Techniques'}
                  </h3>
                </div>
                <p className="text-red-800 text-sm">{t('blog.socialEngineeringProtection.detail1')}</p>
              </div>

              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Eye className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {i18n.language === 'zh' ? '建立验证流程' : 'Establish Verification Process'}
                  </h3>
                </div>
                <p className="text-blue-800 text-sm">{t('blog.socialEngineeringProtection.detail2')}</p>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-900">
                    {i18n.language === 'zh' ? '限制信息暴露' : 'Limit Information Exposure'}
                  </h3>
                </div>
                <p className="text-green-800 text-sm">{t('blog.socialEngineeringProtection.detail3')}</p>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Users className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-purple-900">
                    {i18n.language === 'zh' ? '建立安全文化' : 'Build Security Culture'}
                  </h3>
                </div>
                <p className="text-purple-800 text-sm">{t('blog.socialEngineeringProtection.detail4')}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {i18n.language === 'zh' ? '常见社交工程攻击类型' : 'Common Social Engineering Attack Types'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '紧急情况诈骗' : 'Urgency Scams'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '制造紧急情况，迫使目标快速做出决定而不仔细思考' : 'Create urgent situations to force targets to make quick decisions without careful thought'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '权威诱导' : 'Authority Manipulation'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '冒充权威人士或机构，利用人们对权威的服从心理' : 'Impersonate authority figures or institutions, exploiting people\'s deference to authority'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '互惠原理' : 'Reciprocity Principle'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '先提供小恩小惠，然后要求更大的回报' : 'Provide small favors first, then ask for larger returns'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '社会认同' : 'Social Proof'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '声称其他人都在做某事，利用从众心理' : 'Claim that others are doing something, exploiting herd mentality'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '恐惧诱导' : 'Fear Induction'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '制造恐惧情绪，让目标在恐慌中做出错误决定' : 'Create fear emotions to make targets make wrong decisions in panic'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '好奇心利用' : 'Curiosity Exploitation'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '利用人们的好奇心，诱导点击链接或下载文件' : 'Exploit people\'s curiosity to induce clicking links or downloading files'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-blue-900">
                {i18n.language === 'zh' ? '防护策略' : 'Protection Strategies'}
              </h3>
              <div className="space-y-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '个人防护措施' : 'Personal Protection Measures'}
                  </h4>
                  <ul className="text-gray-600 text-sm space-y-1">
                    <li>• {i18n.language === 'zh' ? '保持怀疑态度，特别是对未经请求的联系' : 'Maintain skepticism, especially for unsolicited contacts'}</li>
                    <li>• {i18n.language === 'zh' ? '通过独立渠道验证身份和请求' : 'Verify identity and requests through independent channels'}</li>
                    <li>• {i18n.language === 'zh' ? '限制在社交媒体上分享的个人信息' : 'Limit personal information shared on social media'}</li>
                    <li>• {i18n.language === 'zh' ? '定期更新隐私设置' : 'Regularly update privacy settings'}</li>
                  </ul>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '企业防护措施' : 'Enterprise Protection Measures'}
                  </h4>
                  <ul className="text-gray-600 text-sm space-y-1">
                    <li>• {i18n.language === 'zh' ? '建立明确的信息验证流程' : 'Establish clear information verification processes'}</li>
                    <li>• {i18n.language === 'zh' ? '定期进行社交工程攻击模拟测试' : 'Regularly conduct social engineering attack simulation tests'}</li>
                    <li>• {i18n.language === 'zh' ? '提供员工安全意识培训' : 'Provide employee security awareness training'}</li>
                    <li>• {i18n.language === 'zh' ? '建立安全事件报告机制' : 'Establish security incident reporting mechanisms'}</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-yellow-900">
                {i18n.language === 'zh' ? '真实案例分析' : 'Real Case Analysis'}
              </h3>
              <div className="space-y-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '案例1：CEO诈骗' : 'Case 1: CEO Fraud'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '攻击者冒充CEO，通过邮件要求财务人员紧急转账。利用了权威和紧急性心理。' : 'Attackers impersonate CEOs and request urgent transfers from finance staff via email. Exploits authority and urgency psychology.'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">
                    {i18n.language === 'zh' ? '案例2：技术支持诈骗' : 'Case 2: Tech Support Scam'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '冒充技术支持人员，声称用户电脑有问题，要求远程访问权限。利用了恐惧和权威心理。' : 'Impersonate tech support staff, claim user\'s computer has problems, request remote access. Exploits fear and authority psychology.'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-green-900">
                {i18n.language === 'zh' ? '关键防护原则' : 'Key Protection Principles'}
              </h3>
              <ol className="list-decimal list-inside text-green-800 space-y-2">
                <li>{i18n.language === 'zh' ? '验证，验证，再验证 - 通过独立渠道确认身份' : 'Verify, verify, verify - confirm identity through independent channels'}</li>
                <li>{i18n.language === 'zh' ? '慢下来思考 - 不要被紧急情况冲昏头脑' : 'Slow down and think - don\'t be overwhelmed by urgent situations'}</li>
                <li>{i18n.language === 'zh' ? '保护个人信息 - 限制公开分享的信息' : 'Protect personal information - limit publicly shared information'}</li>
                <li>{i18n.language === 'zh' ? '建立安全文化 - 鼓励报告可疑活动' : 'Build security culture - encourage reporting suspicious activities'}</li>
                <li>{i18n.language === 'zh' ? '持续学习 - 了解最新的攻击手段' : 'Continuous learning - understand the latest attack methods'}</li>
              </ol>
            </div>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default SocialEngineeringProtection;
