import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';
import { Smartphone, Shield, Key, Lock } from 'lucide-react';

const TwoFactorAuthentication = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.twoFactorAuthentication.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.twoFactorAuthentication.content').slice(0, 160)} />
        <meta name="keywords" content="2FA, two-factor authentication, security, MFA, 双重认证, 多因素认证, 安全" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/two-factor-authentication`} />
        <meta property="og:title" content={`${t('blog.twoFactorAuthentication.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.twoFactorAuthentication.content').slice(0, 160)} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog/two-factor-authentication`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <article className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('blog.twoFactorAuthentication.title')}</h1>
            
            <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-8">
              <div className="flex">
                <Shield className="h-5 w-5 text-green-400 mr-2 mt-0.5" />
                <p className="text-green-700 text-sm">
                  {i18n.language === 'zh' ? '启用双重认证可以将账户被盗风险降低99.9%以上。' : 'Enabling two-factor authentication can reduce account compromise risk by over 99.9%.'}
                </p>
              </div>
            </div>

            <div className="text-gray-700 leading-relaxed mb-8">
              {t('blog.twoFactorAuthentication.content').split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Lock className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {i18n.language === 'zh' ? '优先账户' : 'Priority Accounts'}
                  </h3>
                </div>
                <p className="text-blue-800 text-sm">{t('blog.twoFactorAuthentication.detail1')}</p>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Smartphone className="h-6 w-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-900">
                    {i18n.language === 'zh' ? '认证应用' : 'Authenticator Apps'}
                  </h3>
                </div>
                <p className="text-green-800 text-sm">{t('blog.twoFactorAuthentication.detail2')}</p>
              </div>

              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Key className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg font-semibold text-purple-900">
                    {i18n.language === 'zh' ? '备份代码' : 'Backup Codes'}
                  </h3>
                </div>
                <p className="text-purple-800 text-sm">{t('blog.twoFactorAuthentication.detail3')}</p>
              </div>

              <div className="bg-orange-50 p-6 rounded-lg">
                <div className="flex items-center mb-3">
                  <Shield className="h-6 w-6 text-orange-600 mr-2" />
                  <h3 className="text-lg font-semibold text-orange-900">
                    {i18n.language === 'zh' ? '定期检查' : 'Regular Review'}
                  </h3>
                </div>
                <p className="text-orange-800 text-sm">{t('blog.twoFactorAuthentication.detail4')}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {i18n.language === 'zh' ? '2FA方法对比' : '2FA Methods Comparison'}
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-3">{i18n.language === 'zh' ? '方法' : 'Method'}</th>
                      <th className="text-left py-2 px-3">{i18n.language === 'zh' ? '安全性' : 'Security'}</th>
                      <th className="text-left py-2 px-3">{i18n.language === 'zh' ? '便利性' : 'Convenience'}</th>
                      <th className="text-left py-2 px-3">{i18n.language === 'zh' ? '推荐度' : 'Recommended'}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="py-2 px-3">{i18n.language === 'zh' ? '认证应用' : 'Authenticator App'}</td>
                      <td className="py-2 px-3 text-green-600">★★★★★</td>
                      <td className="py-2 px-3 text-green-600">★★★★☆</td>
                      <td className="py-2 px-3 text-green-600">{i18n.language === 'zh' ? '强烈推荐' : 'Highly Recommended'}</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-2 px-3">{i18n.language === 'zh' ? '硬件令牌' : 'Hardware Token'}</td>
                      <td className="py-2 px-3 text-green-600">★★★★★</td>
                      <td className="py-2 px-3 text-yellow-600">★★★☆☆</td>
                      <td className="py-2 px-3 text-green-600">{i18n.language === 'zh' ? '推荐' : 'Recommended'}</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-2 px-3">{i18n.language === 'zh' ? '短信验证' : 'SMS'}</td>
                      <td className="py-2 px-3 text-yellow-600">★★★☆☆</td>
                      <td className="py-2 px-3 text-green-600">★★★★★</td>
                      <td className="py-2 px-3 text-yellow-600">{i18n.language === 'zh' ? '一般' : 'Acceptable'}</td>
                    </tr>
                    <tr>
                      <td className="py-2 px-3">{i18n.language === 'zh' ? '邮件验证' : 'Email'}</td>
                      <td className="py-2 px-3 text-red-600">★★☆☆☆</td>
                      <td className="py-2 px-3 text-green-600">★★★★☆</td>
                      <td className="py-2 px-3 text-red-600">{i18n.language === 'zh' ? '不推荐' : 'Not Recommended'}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg mb-8">
              <h3 className="text-xl font-semibold mb-4 text-blue-900">
                {i18n.language === 'zh' ? '推荐的认证应用' : 'Recommended Authenticator Apps'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">Google Authenticator</h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '简单易用，支持多种服务，免费' : 'Simple to use, supports multiple services, free'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">Authy</h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '支持云同步，多设备使用，备份功能' : 'Cloud sync support, multi-device usage, backup features'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">Microsoft Authenticator</h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '与微软服务深度集成，支持推送通知' : 'Deep integration with Microsoft services, push notifications'}
                  </p>
                </div>
                <div className="bg-white p-4 rounded border">
                  <h4 className="font-semibold text-gray-800 mb-2">1Password</h4>
                  <p className="text-gray-600 text-sm">
                    {i18n.language === 'zh' ? '集成密码管理器，一站式安全解决方案' : 'Integrated password manager, all-in-one security solution'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-yellow-900">
                {i18n.language === 'zh' ? '重要提醒' : 'Important Reminders'}
              </h3>
              <ul className="list-disc list-inside text-yellow-800 space-y-2">
                <li>{i18n.language === 'zh' ? '务必保存备份代码，并存储在安全的地方' : 'Always save backup codes and store them securely'}</li>
                <li>{i18n.language === 'zh' ? '不要将备份代码截图保存在手机相册中' : 'Do not save backup code screenshots in your phone gallery'}</li>
                <li>{i18n.language === 'zh' ? '定期检查已启用2FA的账户列表' : 'Regularly review your list of 2FA-enabled accounts'}</li>
                <li>{i18n.language === 'zh' ? '更换手机前记得转移认证应用数据' : 'Remember to transfer authenticator app data before changing phones'}</li>
              </ul>
            </div>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default TwoFactorAuthentication;
