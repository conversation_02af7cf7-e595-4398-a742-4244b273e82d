/* Additional global styles */
.bg-dark {
  background-color: hsl(var(--dark));
  color: white;
}

.bg-dark-light {
  background-color: hsl(var(--dark-light));
}

.bg-lighter-gray {
  background-color: hsl(var(--lighter-gray));
}

.bg-light-gray {
  background-color: hsl(var(--light-gray));
}

.text-dark {
  color: hsl(var(--dark));
}

.text-dark-light {
  color: hsl(var(--dark-light));
}

.text-primary {
  color: hsl(var(--primary));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.text-secondary {
  color: hsl(var(--success));
}

.bg-secondary {
  background-color: hsl(var(--success));
}

.accent-primary {
  accent-color: hsl(var(--primary));
}

/* Password length slider customizations */
input[type="range"] {
  -webkit-appearance: none;
  height: 7px;
  background: hsl(var(--light-gray));
  border-radius: 5px;
  background-image: linear-gradient(hsl(var(--primary)), hsl(var(--primary)));
  background-repeat: no-repeat;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: hsl(var(--primary));
  cursor: pointer;
  box-shadow: 0 0 2px 0 #555;
}

input[type="range"]::-webkit-slider-runnable-track {
  -webkit-appearance: none;
  box-shadow: none;
  border: none;
  background: transparent;
}

/* RTL support */
html[dir="rtl"] .fa-chevron-down {
  transform: rotate(180deg);
}

html[dir="rtl"] .rotate-180 {
  transform: rotate(0deg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .md\:flex {
    flex-direction: column;
  }
  
  .md\:w-2\/5, .md\:w-3\/5 {
    width: 100%;
  }
}
