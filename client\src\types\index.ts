export interface PasswordOptions {
  length: number;
  uppercase: boolean;
  lowercase: boolean;
  numbers: boolean;
  symbols: boolean;
}

export interface PasswordStrength {
  score: number; // 0-4
  label: string;
  color: string;
  percentage: number;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface LanguageOption {
  code: string;
  name: string;
  nativeName: string;
}
