{"name": "password-generator-web", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "build:static": "vite build", "preview": "vite preview", "check": "tsc"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.13.1", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.453.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "wouter": "^3.3.5", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "typescript": "5.6.3", "vite": "^5.4.19"}}