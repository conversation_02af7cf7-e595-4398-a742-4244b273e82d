#!/usr/bin/env node

/**
 * 批量替换域名的临时脚本
 * 将 password-generator.me 替换为 password-generator.me
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// 需要处理的文件类型
const FILE_EXTENSIONS = [
  '.html', '.js', '.jsx', '.ts', '.tsx', '.json', '.xml', '.txt', '.css', '.md'
];

// 需要排除的目录
const EXCLUDE_DIRS = [
  'node_modules', 'dist', '.git', '.github', '.vscode'
];

// 替换规则
const REPLACEMENTS = [
  { from: 'https://password-generator.me', to: 'https://password-generator.me' },
  { from: 'https://password-generator.me', to: 'https://password-generator.me' },
  { from: 'password-generator.me', to: 'password-generator.me' },
  { from: '@password_generator_me', to: '@password_generator_me' }
];

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.resolve(__dirname, '..');

// 统计信息
let stats = {
  filesScanned: 0,
  filesModified: 0,
  replacementsCount: 0
};

/**
 * 检查文件是否应该被处理
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return FILE_EXTENSIONS.includes(ext);
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    stats.filesScanned++;

    // 读取文件内容
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let replacementCount = 0;

    // 应用所有替换规则
    for (const { from, to } of REPLACEMENTS) {
      const regex = new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      const matches = content.match(regex);

      if (matches) {
        replacementCount += matches.length;
        content = content.replace(regex, to);
      }
    }

    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      stats.filesModified++;
      stats.replacementsCount += replacementCount;
      console.log(`✅ 已修改: ${filePath} (${replacementCount} 处替换)`);
    }
  } catch (error) {
    console.error(`❌ 处理文件时出错: ${filePath}`, error);
  }
}

/**
 * 递归处理目录
 */
function processDirectory(dirPath) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // 跳过排除的目录
        if (!EXCLUDE_DIRS.includes(entry.name)) {
          processDirectory(fullPath);
        }
      } else if (entry.isFile() && shouldProcessFile(fullPath)) {
        processFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`❌ 处理目录时出错: ${dirPath}`, error);
  }
}

// 主函数
function main() {
  console.log('🔄 开始批量替换域名...');
  console.log(`📁 项目根目录: ${ROOT_DIR}`);

  const startTime = Date.now();

  // 处理整个项目
  processDirectory(ROOT_DIR);

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  console.log('\n📊 统计信息:');
  console.log(`   扫描文件数: ${stats.filesScanned}`);
  console.log(`   修改文件数: ${stats.filesModified}`);
  console.log(`   替换次数: ${stats.replacementsCount}`);
  console.log(`   耗时: ${duration} 秒`);
  console.log('\n✨ 域名替换完成!');
}

// 执行主函数
main();
