import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";

export async function registerRoutes(app: Express): Promise<Server> {
  // Define API routes for password generator app
  // These routes are minimal since most functionality is client-side
  
  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({ status: "ok", message: "Password generator API is running" });
  });

  // Get supported languages endpoint
  app.get("/api/languages", (req, res) => {
    const supportedLanguages = [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'zh', name: 'Chinese', nativeName: '中文' },
      { code: 'es', name: 'Spanish', nativeName: 'Español' },
      { code: 'de', name: 'German', nativeName: 'Deutsch' },
      { code: 'fr', name: 'French', nativeName: 'Français' },
      { code: 'ja', name: 'Japanese', nativeName: '日本語' },
      { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
      { code: 'ru', name: 'Russian', nativeName: 'Русский' },
      { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
      { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' }
    ];
    
    res.json(supportedLanguages);
  });
  
  // Endpoint to validate password strength
  // This could be used as an alternative to client-side validation
  app.post("/api/validate-password", (req, res) => {
    const { password } = req.body;
    
    if (!password) {
      return res.status(400).json({ error: "Password is required" });
    }
    
    // Simple password strength calculation
    let score = 0;
    
    // Length check
    if (password.length >= 12) {
      score += 2;
    } else if (password.length >= 8) {
      score += 1;
    }
    
    // Character variety checks
    if (/[a-z]/.test(password)) score += 1; // lowercase
    if (/[A-Z]/.test(password)) score += 1; // uppercase
    if (/[0-9]/.test(password)) score += 1; // numbers
    if (/[^A-Za-z0-9]/.test(password)) score += 1; // symbols
    
    // Determine strength based on score
    let strength = 'weak';
    if (score >= 5) {
      strength = 'very strong';
    } else if (score >= 4) {
      strength = 'strong';
    } else if (score >= 3) {
      strength = 'medium';
    } else if (score >= 2) {
      strength = 'weak';
    } else {
      strength = 'very weak';
    }
    
    res.json({
      score,
      strength,
      feedback: {
        suggestions: getSuggestions(password, score)
      }
    });
  });
  
  // Optional endpoint for logging password strength analytics
  // No actual passwords are stored, just anonymous strength metrics
  app.post("/api/password-metrics", (req, res) => {
    const { strength, length, hasUppercase, hasLowercase, hasNumbers, hasSymbols } = req.body;
    
    // In a real implementation, this would store metrics for analytics
    // For this demo, we'll just return a success response
    res.json({ success: true });
  });

  const httpServer = createServer(app);

  return httpServer;
}

// Helper function to provide password improvement suggestions
function getSuggestions(password: string, score: number): string[] {
  const suggestions = [];
  
  if (password.length < 12) {
    suggestions.push("Make your password longer (at least 12 characters)");
  }
  
  if (!/[a-z]/.test(password)) {
    suggestions.push("Add lowercase letters");
  }
  
  if (!/[A-Z]/.test(password)) {
    suggestions.push("Add uppercase letters");
  }
  
  if (!/[0-9]/.test(password)) {
    suggestions.push("Add numbers");
  }
  
  if (!/[^A-Za-z0-9]/.test(password)) {
    suggestions.push("Add special characters (!@#$%&)");
  }
  
  if (/(.)\1{2,}/.test(password)) {
    suggestions.push("Avoid repeating characters");
  }
  
  if (/^(password|123456|qwerty)/i.test(password)) {
    suggestions.push("Avoid commonly used passwords");
  }
  
  return suggestions;
}
